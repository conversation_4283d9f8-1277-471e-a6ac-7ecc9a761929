import React, { useState } from 'react'
import {
  <PERSON>rid,
  IconButton,
  <PERSON><PERSON>graphy, <PERSON>ton,
  DialogContent,
  TextField
} from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { usePortalStore } from '../../store/userPortalSlice'
import subscriptionQuery from '../../reactQuery/subscriptionQuery'
import toast from 'react-hot-toast'

const CancelTMFSubscription = ({ subscriptionId, refetchSubscriptions }) => {
  const portalStore = usePortalStore((state) => state)
  const [cancellationReason, setCancellationReason] = useState('')

  const cancelSubscription = subscriptionQuery.useCancelSubscriptionMutation({
    onSuccess: (res) => {
      if (res?.data?.success) {
        toast.success('Your subscription has been canceled successfully.')
        refetchSubscriptions()
        portalStore.closePortal()
      }
    },
    onError: (error) => {
      toast.error('An error occurred while canceling your subscription. Please try again later.')
      console.log('error', error)
      portalStore.closePortal()
    }
  })

  const handleClose = () => {
    portalStore.closePortal()
  }

  const handleConfirm = () => {
    if (cancellationReason === '') {
      toast.error('Please enter a cancellation reason.')
      return
    }
    cancelSubscription.mutate({
      userSubscriptionId: subscriptionId,
      cancellationReason: cancellationReason
    })
  }

  return (
    <>
      <Grid className='inner-modal-header'>
        <Typography variant='h4'> Confirmation </Typography>
        <Grid className='modal-close'>
          <IconButton edge='start' color='inherit' onClick={handleClose} aria-label='close'>
            <CloseIcon />
          </IconButton>
        </Grid>
      </Grid>
      <DialogContent className='payment-status-modal'>
        <Grid className='payment-status-content'>
          <Typography>Are you sure you want to cancel your subscription ?</Typography>
          <Typography>Please enter a cancellation reason</Typography>
          <TextField
            id='outlined-multiline-flexible'
            label='Multiline'
            value={cancellationReason}
            onChange={(e) => setCancellationReason(e.target.value)}
            multiline
            maxRows={3}
          />
          <Button onClick={() => handleConfirm('yes')}>Yes</Button>
          <Button onClick={handleClose}>No</Button>
        </Grid>
      </DialogContent>
    </>
  )
}

export default CancelTMFSubscription
