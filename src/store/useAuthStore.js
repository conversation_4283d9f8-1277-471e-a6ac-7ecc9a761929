import { create } from 'zustand'

import { deleteC<PERSON><PERSON>, get<PERSON><PERSON><PERSON>, setC<PERSON>ie } from '../utils/cookiesCollection'

// Cache the initial cookie check to avoid repeated DOM queries
const initialPathCookieCheck = (() => {
  try {
    return getCookie('path') ? true : false
  } catch (error) {
    console.warn('<PERSON><PERSON> check failed:', error)
    return false
  }
})()

const useAuthStore = create((set) => ({
  isAuthenticated: false,
  pathCookieCheck: initialPathCookieCheck,
  setAuthenticated: (value) => {
    set({ isAuthenticated: value })
  },
  setPathCookieCheck: (value) => {
    if (value) {
      setCookie('path', '/home', 30)
    } else {
      deleteCookie('path')
    }
    set({ pathCookieCheck: value })
  }
}))

export default useAuthStore
