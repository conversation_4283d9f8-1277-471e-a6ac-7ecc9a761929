import { makeStyles } from '@mui/styles'

import { LobbyRight } from '../../MainPage.styles'
import { colors } from '@mui/material'

export default makeStyles((theme) => ({
  lobbyRight: {
    ...LobbyRight(theme),
    background: '#000000 !important'
  },
  container: {
    maxWidth: '103rem',
    padding: '1rem',
    margin: '0 auto'
  },
  title: {
    marginBottom: 16,
    color: '#FDB72E',
    fontWeight: 'bold',
    fontSize: 20
  },
  featuresTable: {
    display: 'grid',
    gridTemplateColumns: '1.5fr repeat(auto-fit, minmax(120px, 1fr))',
    background: '#111',
    borderRadius: 12,
    padding: '12px 16px',
    marginBottom: 20
  },
  featureRow: {
    display: 'contents'
  },
  featureCellHeader: {
    fontWeight: 'bold',
    color: '#FDB72E',
    padding: '8px 0'
  },
  planHeader: {
    textAlign: 'center',
    fontWeight: 'bold',
    color: '#666',
    padding: '8px 0'
  },
  selectedHeader: {
    color: '#FDB72E',
    borderBottom: '1px solid #FDB72E'
  },
  featureCell: {
    padding: '6px 0',
    fontSize: '1rem',
    color: '#C4C4C4',
    fontWeight: '600'
  },
  featureCheck: {
    textAlign: 'center',
    fontSize: '1.25rem',
    padding: '6px 0'
  },
  selectedPlanBox: {
    background: '#1a1a1a',
    borderLeft: '1px solid #FDB72E',
    borderRight: '1px solid #FDB72E'
  },
  selectedPlanBoxBottom: {
    borderBottom: '1px solid #FDB72E'
  },
  currentPlanBox: {
    background: '#1a1a1a',
    borderLeft: '2px solid #f1efeaff',
    borderRight: '2px solid #f1efeaff'
  },
  check: {
    color: 'limegreen',
    fontWeight: 'bold'
  },
  cross: {
    color: 'red',
    fontWeight: 'bold'
  },
  trialBox: {
    background: '#1a1a1a',
    padding: 12,
    margin: '16px 0',
    borderRadius: 8,
    border: '1px solid #ffffff',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  trialText: {
    fontWeight: 'bold',
    color: '##FDB72E'
  },
  trialButton: {
    background: 'transparent',
    border: '1px solid white',
    fontWeight: '600',
    padding: '6px 12px',
    color: 'white',
    cursor: 'pointer',
    borderRadius: 6,
    '&:hover': {
      background: '#FDB72E',
      color: 'black',
      border: '1px solid #fdb72e'
    }
  },
  plansWrapper: {
    display: 'flex',
    justifyContent: 'space-between',
    margin: '16px 0'
  },
  planBox: {
    flex: 1,
    margin: '0 4px',
    padding: 12,
    borderRadius: 8,
    border: '1px solid transparent',
    background: '#171717',
    textAlign: 'center',
    transition: '0.3s',
    '&:hover': {
      cursor: 'pointer',
      opacity: 0.9
    }
  },
  selectedPlan: {
    border: '1px solid #FDB72E',
    background: '#000000'
  },
  planName: {
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'start'
  },
  upgradeText: {
    color: '#1687EA',
    fontWeight: 600,
    fontSize: '21.33px',
    lineHeight: '30px',
    letterSpacing: '0%'
  },
  selectedPlanName: {
    color: '#FDB72E'
  },
  planPrice: {
    fontSize: '1.5rem',
    fontWeight: 'bold',
    // marginBottom: 4,
    color: '#FDB72E'
  },
  featureValue: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    marginLeft: 4
  },
  terms: {
    display: 'flex',
    alignItems: 'center',
    marginTop: 12,
    fontWeight: '600',
    fontSize: 13,
    color: '#C4C4C4',
    '& .MuiCheckbox-root': {
      color: '#FDB72E'
    }
  },
  continueButton: {
    width: '100%',
    marginTop: 16,
    background: '#FDB72E',
    border: 'none',
    padding: 12,
    fontWeight: 'bold',
    color: 'black',
    borderRadius: 8,
    cursor: 'pointer',
    fontSize: 16,
    '&:disabled': {
      background: '#555',
      color: '#aaa',
      cursor: 'not-allowed'
    }
  },

  btnWrap: {
    display: 'flex',
    justifyContent: 'center',
    '& .btn-primary': {
      textAlign: 'center',
      maxWidth: '400px',
      width: '100%',
      fontSize: '1rem',
      fontWeight: '600',
      background: '#FFA538',
      borderRadius: '10px !important'
    }
  },
  subscriptionImageWrap: {
    display: 'flex',
    flexDirection: 'column !important',
    gap: '2rem',
    position: 'relative',
    overflow: 'auto',
    scrollBehavior: 'smooth',
    animation: 'scrollVertical 16s linear infinite'
  },
  subscriptionImage: {
    height: '100%',
    width: '100%',
    position: 'relative',
    borderRadius: '1rem',
    maxHeight: '400px',
    overflow: 'hidden',
    '& img': {
      height: '100%',
      width: '100%',
      objectFit: 'cover'
    }
  },
  subscriptionImageOverlay: {
    position: 'absolute',
    top: '0',
    left: '0',
    height: '100%',
    width: '100%',
    background:
      'radial-gradient(50% 119.45% at 50% 50%, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.675481) 54.81%, #000000 100%)'
  },
  overlayContent: {
    position: 'absolute',
    left: '0',
    display: 'flex',
    alignItems: 'center',
    flexDirection: 'column',
    textAlign: 'center',
    paddingTop: '6rem',
    top: '0',
    height: '100%',
    width: '100%',
    '& h3': {
      fontSize: '2rem',
      fontWeight: '700',
      background: 'linear-gradient(180deg, #F7E041 21.88%, #FA8C00 73.44%)',
      WebkitBackgroundClip: 'text',
      textShadow: '0px 4.09px 6.13px 0px #A3A1A199',
      WebkitTextFillColor: 'transparent'
    },
    '& p': {
      fontSize: '1.25rem',
      fontWeight: '700'
    }
  },
  leftSection: {
    position: 'relative',
    overflow: 'hidden',
    maxHeight: '37.5rem',
    [theme.breakpoints.down('md')]: {
      maxHeight: '16.5rem'
    }
  },
  rightSection: {
    borderRadius: '0.5rem',
    padding: '2rem',
    boxShadow: '0px 0px 6px 0px #92650D'
  },
  planDetail: {
    display: 'flex',
    alignItems: 'center',
    color: '#515151',
    fontWeight: '600'
  }
}))
