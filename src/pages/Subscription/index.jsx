import React, { useEffect, useState } from 'react'
import { Box, Button, Checkbox, Grid, Typography, FormControlLabel } from '@mui/material'
import subscriptionQuery from '../../reactQuery/subscriptionQuery'
import { useSubscriptionStore } from '../../store/useSubscriptionStore'
import { usePortalStore } from '../../store/store'
import PaymentStatus from '../../components/PaymentStatus'
import StepperForm from '../../components/StepperForm'
import { useLocation } from 'react-router-dom'
import useStyles from './Subscription.styles'
import { Clear, Done } from '@mui/icons-material'
import VipOne from '../../components/ui-kit/icons/webp/vip-1.webp'
import VipTwo from '../../components/ui-kit/icons/webp/vip-2.webp'

const vipImages = [VipOne, VipTwo] // Add more if needed

const Subscriptions = () => {
  const classes = useStyles()
  const location = useLocation()
  const portalStore = usePortalStore((state) => state)
  const { userSubscription, setUserSubscription, selectedPlan, setSelectedPlan, setIsTrial, setRefundAmount, setRemainingDays, setPaymentData } =
    useSubscriptionStore((state) => state)

  const [subscriptionsData, setSubscriptionsData] = useState([])
  const [currentPlan, setCurrentPlan] = useState(null)
  const [isUpgrade, setIsUpgrade] = useState(false)
  const [acceptedTerms, setAcceptedTerms] = useState(false)

  const { mutate: getSubscriptionStatus } = subscriptionQuery.getSubscriptionMutation({
    onSuccess: (res) => {
      setUserSubscription(res?.data?.data)
      refetchSubscriptions()
    },
    onError: (error) => console.log('Error getting profile:', error)
  })

  const { data, refetch: refetchSubscriptions } = subscriptionQuery.getSubscriptionDataQuery({
    successToggler: (res) => {
      if (res.currentSubscriptionPlan) {
        setSubscriptionsData([...res.data, res.currentSubscriptionPlan])
      } else {
        setSubscriptionsData(res.data)
      }
      setCurrentPlan(res?.currentSubscriptionPlan || null)

      setIsUpgrade(res?.upgradeSubscription || false)
      if (res?.data?.length) setSelectedPlan(res.data[0])
    }
  })

  const isSubscribed = userSubscription?.subscriptionDetail !== null
  const canCheckUpgrade = isSubscribed && isUpgrade && !!selectedPlan?.subscriptionId

  const allFeatures = [...new Set(subscriptionsData.flatMap((sub) => sub.features.map((f) => f.featureDetail.name)))]

  const handleSelectPlan = (plan) => {
    setSelectedPlan(plan)
    if (canCheckUpgrade) {
      setRefundAmount(plan?.amount - plan?.adjustedAmount)
      setRemainingDays(plan?.durationDays)
      setPaymentData('finalAmount', data?.data?.adjustedAmount)
    }
  }

  const handleBuyNow = (isTrial) => {
    if (!acceptedTerms || !selectedPlan) return
    setIsTrial(isTrial)
    portalStore.openPortal(
      () => (
        <Box className='stepper-outer-box'>
          <StepperForm stepperCalledFor='subscription_purchase' packageDetails={selectedPlan} />
        </Box>
      ),
      'StepperModal'
    )
  }

  const handlePaymentSuccess = (data) => {
    portalStore.openPortal(() => <PaymentStatus paymentDetails={data} />, 'paymentmodal')
    getSubscriptionStatus()
  }

  useEffect(() => {
    const params = new URLSearchParams(location.search)
    const statuss = params.get('status')
    if (statuss === 'success' || statuss === 'failed' || statuss === 'cancelled') {
      const data = {
        transactionId: params.get('transactionId'),
        status: statuss,
        paymentMethod: params.get('paymentMethod'),
        scCoin: params.get('scCoin'),
        gcCoin: params.get('gcCoin'),
        bonusSc: params.get('bonusSc'),
        bonusGc: params.get('bonusGc'),
        amount: params.get('amount')
      }
      handlePaymentSuccess(data)
    }
    refetchSubscriptions()
  }, [location])

  return (
    <Grid className={classes.lobbyRight}>
      <Grid container spacing={2} className={classes.container}>
        {/* Left Side Scroll VIP Images */}
        <Grid item xs={12} md={4}>
          <Grid className={classes.leftSection}>
            <Grid className={classes.subscriptionImageWrap}>
              <Box className={classes.scrollWrapper}>
                {[...vipImages, ...vipImages].map((image, index) => (
                  <Box key={index} className={classes.subscriptionImage}>
                    <img src={image} alt={`vip-${index}`} loading='lazy' />
                    <Box className={classes.subscriptionImageOverlay} />
                  </Box>
                ))}
              </Box>
            </Grid>
            <Box className={classes.overlayContent}>
              <Typography variant='h3'>Go Premium — Stay Ahead!</Typography>
              <Typography variant='body1'>
                Access Daily Rewards, Special Packages, and Exclusive Tournaments.
              </Typography>
            </Box>
          </Grid>
        </Grid>

        {/* Right Side Content */}
        <Grid item xs={12} md={8}>
          {/* Header Row */}
          <div
            className={classes.featuresTable}
            style={{ gridTemplateColumns: `1.5fr repeat(${subscriptionsData.length}, 1fr)` }}
          >
            <div className={classes.featureRow}>
              <div className={classes.featureCellHeader}>FEATURES</div>
              {subscriptionsData.map((plan) => {
                return (
                  <div
                    key={plan.subscriptionId}
                    className={`${classes.planHeader} ${selectedPlan?.name === plan.name ? classes.selectedHeader : ''}`}
                  >
                    {plan.subscriptionId === currentPlan?.subscriptionId
                      ? (<div className={classes.currentLabel}>CURRENT</div>)
                      : plan.name.toUpperCase()}
                  </div>
                )
              })}
            </div>

            {/* Feature Rows */}
            {allFeatures.map((featureName, idx) => (
              <div className={classes.featureRow} key={idx}>
                <div className={classes.featureCell}>{featureName}</div>
                {subscriptionsData.map((plan) => {
                  const featureObj = plan.features.find((f) => f.featureDetail.name === featureName)
                  const isCurrentPlan = plan.subscriptionId === currentPlan?.subscriptionId
                  const value = featureObj?.featureValue
                  const invalidValues = ['true', true, 'false', false, '0', 0, null, undefined]
                  const isNumeric = typeof value === 'number'
                  const isLastRow = idx === allFeatures.length - 1

                  return (
                    <div
                      key={`${plan.subscriptionId}-${featureName}`}
                      className={`${classes.featureCheck} ${selectedPlan?.name === plan.name ? classes.selectedPlanBox : ''
                        } ${selectedPlan?.name === plan.name && isLastRow ? classes.selectedPlanBoxBottom : ''}
                        ${isCurrentPlan ? classes.currentPlanBox : ''}`}
                    >
                      {featureObj
                        ? (
                          <span className={classes.check}>
                            {!invalidValues.includes(value)
                              ? (isNumeric
                                  ? (
                                    <span className={classes.featureValue}>({value})</span>)
                                  : (
                                    <span className={classes.featureValue}>{value}</span>))
                              : <Done />}
                          </span>)
                        : (
                          <span className={classes.cross}>
                            <Clear />
                          </span>)}
                    </div>
                  )
                })}
              </div>
            ))}
          </div>

          {/* Trial Option */}
          {!isSubscribed && selectedPlan?.isTrialAllowed && (
            <div className={classes.trialBox}>
              <div className={classes.trialText}>Start {data?.subscriptionTrialData?.trialDays} days Free trial</div>
              <button className={classes.trialButton} onClick={() => handleBuyNow(true)}>
                Start trial
              </button>
            </div>
          )}

          {/* Plans Section */}
          <div className={classes.plansWrapper}>
            {subscriptionsData.map((plan) => {
              const isCurrent = plan.subscriptionId === currentPlan?.subscriptionId
              if (isCurrent) return null

              const isSelected = selectedPlan?.name === plan.name
              const isUpgradeActive = isSelected && canCheckUpgrade
              return (
                <div
                  key={plan.subscriptionId}
                  className={`${classes.planBox} ${isSelected ? classes.selectedPlan : ''}`}
                  onClick={() => handleSelectPlan(plan)}
                >
                  {isUpgradeActive && <div className={classes.upgradeText}>UPGRADE TO</div>}
                  <div className={`${classes.planName} ${isSelected ? classes.selectedPlanName : ''}`}>
                    {plan.name.toUpperCase()}
                  </div>
                  <div className={classes.planPrice}>
                    {isUpgradeActive
                      ? (
                        <>
                          ${plan?.adjustedAmount}
                          <span style={{ marginLeft: 6, textDecoration: 'line-through', color: '#aaa' }}>
                            ${plan.amount}
                          </span>
                        </>)
                      : (
                        <>${plan.amount}</>)}
                  </div>
                  <div> / {plan.durationDays} Days</div>
                </div>
              )
            })}
          </div>

          {/* Terms Checkbox */}
          <FormControlLabel
            control={<Checkbox checked={acceptedTerms} onChange={() => setAcceptedTerms(!acceptedTerms)} />}
            label='Follow all terms and conditions.'
            className={classes.terms}
          />

          {/* Continue Button */}
          <Button
            variant='contained'
            disabled={!acceptedTerms || !selectedPlan}
            onClick={() => handleBuyNow(false)}
            className='btn btn-primary'
            sx={{
              width: '100%',
              alignSelf: 'center',
              fontWeight: 'bold',
              color: 'black'
            }}
          >
            {isUpgrade && isSubscribed
              ? `Upgrade to ${selectedPlan?.name || '...'}`
              : `Continue with ${selectedPlan?.name || '...'}`}
          </Button>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default Subscriptions
