import React from 'react'
import useStyles from '../../Settings.styles'
import {
  Box,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Skeleton
} from '@mui/material'
import AmexImg from '../../../../components/ui-kit/icons/svg/AmexImg.svg'
import MasterImg from '../../../../components/ui-kit/icons/svg/MasterImg.svg'
import VisaImg from '../../../../components/ui-kit/icons/svg/VisaImg.svg'
import DiscoverImg from '../../../../components/ui-kit/icons/svg/DiscoverImg.svg'
import MaestroIcon from '../../../../components/ui-kit/icons/opImages/maestro.webp'
import subscriptionQuery from '../../../../reactQuery/subscriptionQuery'
import { usePortalStore } from '../../../../store/userPortalSlice'
import CancelTMFSubscription from '../../../../components/Subscription/CancelTMFSubscription'
import { useNavigate } from 'react-router-dom'

const Subscriptions = () => {
  const classes = useStyles()
  const navigate = useNavigate()
  const portalStore = usePortalStore((state) => state)

  const { data: subscriptionsData, isLoading, isError, error, refetch } =
    subscriptionQuery.getPlayerSubscriptionDataQuery({
      successToggler: (data) => console.log('### SubscriptionsData', data),
      errorToggler: (err) => console.log('### SubscriptionsError', err)
    })

  const handleCancelSubscription = (id) => {
    portalStore.openPortal(
      () => <CancelTMFSubscription subscriptionId={id} refetchSubscriptions={refetch} />,
      'innerModal'
    )
  }

  if (isLoading) {
    return (
      <Box className='space-y-3'>
        <Skeleton variant='text' width={180} height={30} />
        <Skeleton variant='rectangular' width='100%' height={60} />
        <Skeleton variant='rectangular' width='100%' height={60} />
      </Box>
    )
  }
  if (isError) return <Typography color='error'>{error?.message || 'Something went wrong'}</Typography>

  const rows = subscriptionsData?.data?.rows || []
  const currentPlan = rows.find(plan => plan.status === 'active') || null
  const planHistory = rows.filter(plan => plan !== currentPlan)
  const end = new Date(currentPlan?.endDate)
  const daysRemaining = Math.max(0, Math.ceil((end - new Date()) / (1000 * 60 * 60 * 24)))

  return (
    <Box className={classes.subscriptionsContainer}>
      {/* PLAN DETAILS */}
      <Box className='subscriptions-card'>
        <TableContainer component={Paper}>
          <Table className='subscription-table'>
            <TableHead className='table-head'>
              <TableRow>
                <TableCell className='table-head-text'>Plan Details</TableCell>
                <TableCell className='table-head-text'>Date</TableCell>
                <TableCell className='table-head-text'>Amount</TableCell>
                <TableCell className='table-head-text'>Remaining Days</TableCell>
                <TableCell className='table-head-text'>Status</TableCell>
                <TableCell className='table-head-text'>Trial</TableCell>
                <TableCell className='table-head-text'>Auto Renew</TableCell>
              </TableRow>
            </TableHead>

            <TableBody className='table-body' sx={{ padding: 2 }}>
              {currentPlan && (
                <TableRow className='table-body-row'>
                  {/* PLAN NAME + FEATURES */}
                  <TableCell className='table-item-row'>
                    <Typography className='current-plan-sub-title'>
                      {currentPlan.subscriptionDetail?.name} Plan
                    </Typography>
                    {currentPlan.subscriptionDetail?.features?.map((f, idx) => (
                      <Typography key={idx} className='current-plan-description' variant='body2' color='white'>
                        {f.featureDetail?.name}: {f.featureValue}
                      </Typography>
                    ))}
                  </TableCell>

                  {/* DATE RANGE */}
                  <TableCell className='table-item-row'>
                    {new Date(currentPlan.startDate).toLocaleDateString()} –{' '}
                    {new Date(currentPlan.endDate).toLocaleDateString()}
                  </TableCell>

                  {/* AMOUNT */}
                  <TableCell className='table-item-row'>${currentPlan.subscriptionDetail?.amount}</TableCell>

                  {/* REMAINING DAYS */}
                  <TableCell className='table-item-row'>{daysRemaining}</TableCell>

                  {/* STATUS */}
                  <TableCell className='table-item-row'>
                    {currentPlan.status}
                  </TableCell>

                  {/* FREE TRIAL */}
                  <TableCell className='table-item-row'>{currentPlan.trialPlan ? 'Yes' : 'No'}</TableCell>

                  {/* AUTO RENEW */}
                  <TableCell className='table-item-row'>{currentPlan.autoRenew ? 'Active' : 'Inactive'}</TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
        {/* ACTION BUTTONS */}
        {currentPlan && (
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1, p: 1 }}>
            <Button
              variant='outlined'
              className='upgrade-button'
              onClick={() => navigate('/subscriptions')}
            >
              Upgrade Plan
            </Button>
            {currentPlan?.autoRenew && (
              <Button
                variant='contained'
                className='stop-auto-renew-button'
                onClick={() => handleCancelSubscription(currentPlan.id)}
              >
                Cancel Auto Renew
              </Button>
            )}
          </Box>
        )}
        {!currentPlan && (
          <Typography variant='h6' sx={{ color: 'red', textAlign: 'center', p: 2 }}>No active subscription found.</Typography>
        )}
      </Box>

      {/* PAYMENT METHOD */}
      <Box className='genral-tab'>
        <Typography variant='h6' sx={{ backgroundColor: '#1E1E1E', p: 1 }}>Payment Method</Typography>
        <Box className={classes.prefferdPaymentBox}>
          {currentPlan?.preferredPayment.map((payment, key) => (
            <Box key={key} className='preferred-card'>
              <Box className='card-detail'>
                {payment?.moreDetails.cardType === 'VI' && <img src={VisaImg} alt='Visa' />}
                {payment?.moreDetails.cardType === 'DI' && <img src={DiscoverImg} alt='Discover' />}
                {payment?.moreDetails.cardType === 'MD' && <img src={MaestroIcon} alt='Maestro' />}
                {payment?.moreDetails.cardType === 'MC' && <img src={MasterImg} alt='MasterCard' />}
                {payment?.moreDetails.cardType === 'AM' && <img src={AmexImg} alt='Amex' />}
                <Box className='bank-details'>
                  <Box className='bank-name'>
                    <Typography>{payment?.moreDetails.holderName}</Typography>
                  </Box>
                  <Box className='bank-number'>
                    <Typography className='holder-name border-right'>**** {payment?.moreDetails.lastDigits}</Typography>
                    <Typography className='holder-name'>
                      {payment?.moreDetails.cardExpiry?.month}/{payment?.moreDetails.cardExpiry?.year}
                    </Typography>
                  </Box>
                </Box>
              </Box>
              <Box className='card-remove'>
                <Button variant='text' className='remove-button'>
                  Update
                </Button>
              </Box>
            </Box>
          ))}
        </Box>
      </Box>

      {/* PLAN HISTORY TABLE */}
      <Typography variant='h6'>Plan History</Typography>
      <Box className='subscriptions-card'>
        <TableContainer component={Paper} sx={{ backgroundColor: 'transparent', boxShadow: 'none' }}>
          <Table className='subscription-table'>
            <TableHead className='table-head'>
              <TableRow>
                <TableCell className='table-head-text'>Plan</TableCell>
                <TableCell className='table-head-text'>Features</TableCell>
                <TableCell className='table-head-text'>Date</TableCell>
                <TableCell className='table-head-text'>Amount</TableCell>
              </TableRow>
            </TableHead>

            <TableBody className='table-body'>
              {planHistory.length > 0
                ? (planHistory.map((plan) => (
                  <TableRow key={plan.id} className='table-body-row'>
                    <TableCell className='table-item-row'>{plan.subscriptionDetail?.name} Plan</TableCell>
                    <TableCell className='table-item-row'>
                      {plan.subscriptionDetail?.features?.map((f, idx) => (
                        <Typography key={idx} variant='body2' color='white'>
                          {f.featureDetail?.name}: {f.featureValue}
                        </Typography>
                      ))}
                    </TableCell>
                    <TableCell className='table-item-row'>
                      {new Date(plan.startDate).toLocaleDateString()} -{' '}
                      {new Date(plan.endDate).toLocaleDateString()}
                    </TableCell>
                    <TableCell className='table-item-row'>${plan.subscriptionDetail?.amount}</TableCell>
                  </TableRow>)))
                : (
                  <TableRow>
                    <TableCell colSpan={4} align='center' sx={{ color: 'gray' }}>
                      No previous plans found.
                    </TableCell>
                  </TableRow>)}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
    </Box>
  )
}

export default Subscriptions
